[2m2025-07-31 00:02:34.134[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-31 00:02:34.238[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-31 00:02:34.242[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-31 00:02:34.262[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-31 00:02:34.263[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-31 00:02:34.282[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-31 00:02:36.733[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-07-31 00:02:36.753[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-07-31 00:02:36.755[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-07-31 00:02:36.776[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-07-31 00:02:36.776[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-31 00:02:36.781[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:36.797[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-31 00:02:36.797[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-07-31 00:02:36.804[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:39.204[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-31 00:02:39.211[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-31 00:02:39.211[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-31 00:02:39.213[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-31 00:02:39.213[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-31 00:02:39.229[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-31 00:02:41.246[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-31 00:02:41.263[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-31 00:02:41.266[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-31 00:02:41.281[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL)
[2m2025-07-31 00:02:41.281[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:41.286[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:41.286[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-31 00:02:41.286[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ************(String), 20(Long), 0(Long)
[2m2025-07-31 00:02:41.293[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-31 00:02:41.294[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.308[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.308[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:41.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:41.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.328[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.330[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.330[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.330[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:41.337[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:41.338[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.350[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.351[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.352[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.352[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:41.358[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:41.359[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.371[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.373[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.373[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.373[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:41.380[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:41.380[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.392[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:41.394[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.394[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:41.394[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:41.403[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:45.437[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-31 00:02:45.458[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-31 00:02:45.460[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-31 00:02:45.460[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-31 00:02:45.461[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-07-31 00:02:45.467[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-31 00:02:45.468[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM IN (?))
[2m2025-07-31 00:02:45.483[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM IN (?))
[2m2025-07-31 00:02:45.485[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM IN (?))
[2m2025-07-31 00:02:45.485[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM IN (?))
[2m2025-07-31 00:02:45.486[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:45.492[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:46.254[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-07-31 00:02:46.262[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS FROM LABEL_TEMPLATE WHERE ID=?
[2m2025-07-31 00:02:46.265[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-07-31 00:02:46.265[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE ID = ?
[2m2025-07-31 00:02:46.266[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m ==> Parameters: cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-31 00:02:46.276[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectById                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:46.281[0;39m [33m WARN[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 解析字段映射失败: argument "content" is null
[2m2025-07-31 00:02:46.282[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-31 00:02:46.294[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-31 00:02:46.296[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-31 00:02:46.296[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-31 00:02:46.296[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-07-31 00:02:46.304[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-31 00:02:46.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-07-31 00:02:46.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-07-31 00:02:46.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-07-31 00:02:46.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-07-31 00:02:46.307[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.308[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 110de26c9970764fffbd6af7011d2768 处理模板字段替换完成
[2m2025-07-31 00:02:46.308[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 247550797bd7aa10784121f51394f84b 处理模板字段替换完成
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.309[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 5b26701afbfb65c0a8a833f45581d752 处理模板字段替换完成
[2m2025-07-31 00:02:46.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-07-31 00:02:46.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-07-31 00:02:46.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-07-31 00:02:46.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-07-31 00:02:46.316[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 811424d68b9aea59e781950a5a47c736 处理模板字段替换完成
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {qrcode} -> ZK25070001
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {bankName} -> 中国人民银行
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {specialMark} -> EPQ
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {coinName1} -> 拾元
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 字段替换: {gradeScore} -> Superb Gem Unc68
[2m2025-07-31 00:02:46.317[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.s.impl.BatchPrintServiceImpl    [0;39m [2m:[0;39m 为钱币 f3de6989e3d445c00fa9c159a6dbdba0 处理模板字段替换完成
[2m2025-07-31 00:02:46.318[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.330[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.332[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.332[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.333[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:46.340[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:46.341[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.357[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.359[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.359[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.359[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:46.389[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:46.390[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.405[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.408[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.409[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.410[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:46.417[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:46.418[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.431[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.433[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.433[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.434[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:46.440[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-31 00:02:46.441[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.454[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-31 00:02:46.456[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.456[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-31 00:02:46.456[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-31 00:02:46.464[0;39m [32mDEBUG[0;39m [35m10383[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
