<template>
  <!-- 增强的打印标签预览抽屉 -->
  <el-drawer
    v-model="visible"
    title=""
    direction="rtl"
    size="70%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    class="enhanced-preview-drawer"
  >
    <!-- 工具栏 -->
    <template #header>
      <div class="drawer-header">
        <div class="drawer-title-section">
          <span class="drawer-title">打印标签预览</span>
          <el-tag v-if="printData?.templateName" type="info" size="small">
            {{ printData.templateName }}
          </el-tag>
        </div>
        <div class="drawer-actions">
          <el-button-group>
            <el-button
              :icon="ZoomOut"
              @click="handleZoomOut"
              size="small"
              :disabled="scale <= minScale"
            />
            <el-button
              size="small"
              style="min-width: 60px;"
            >
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button
              :icon="ZoomIn"
              @click="handleZoomIn"
              size="small"
              :disabled="scale >= maxScale"
            />
          </el-button-group>
          <el-button
            :icon="RefreshRight"
            @click="handleRefresh"
            size="small"
          >
            刷新
          </el-button>
          <el-button
            type="primary"
            :icon="Printer"
            @click="handlePrint"
            :loading="printing"
            size="small"
          >
            打印
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownload"
            :loading="downloading"
            size="small"
          >
            下载
          </el-button>
        </div>
      </div>
    </template>

    <!-- 打印标签内容 -->
    <div class="print-content" v-loading="loading">
      <!-- 信息栏 -->
      <div class="print-info-bar">
        <el-alert
          :title="`共 ${printData?.totalCount || 0} 条记录待打印`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 预览区域 -->
      <div class="preview-area" :style="previewAreaStyle">
        <!-- 分页展示所有打印标签 -->
        <div
          v-for="(page, pageIndex) in paginatedTemplates"
          :key="`page-${pageIndex}`"
          class="print-page"
          :style="getPrintPageStyle(pageIndex)"
        >
          <!-- 页面标题 -->
          <div class="page-header">
            <span class="page-number">第 {{ pageIndex + 1 }} 页</span>
            <span class="page-info">共 {{ page.length }} 个标签</span>
          </div>

          <!-- 页面内的标签列表 -->
          <div class="labels-container">
            <div
              v-for="(template, labelIndex) in page"
              :key="`page-${pageIndex}-label-${labelIndex}`"
              class="label-item"
              :style="getLabelItemStyle(labelIndex)"
            >
              <!-- hiprint模板渲染容器 -->
              <div
                :id="`hiprint-preview-container-${template.originalIndex}`"
                class="hiprint-preview-container"
                v-loading="renderLoading && template.originalIndex === 0"
              >
                <!-- hiprint渲染的HTML内容将在这里显示 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { EleMessage } from 'ele-admin-plus/es';
import {
  Printer,
  Download,
  ZoomIn,
  ZoomOut,
  RefreshRight
} from '@element-plus/icons-vue';

// 导入hiprint配置工具
import {
  initHiprint as initHiprintPlugin,
  createPrintTemplate,
  getHiprintInstance,
  handleHiprintError
} from '@/utils/hiprint-config';

// hiprint实例
let hiprintTemplate = null;

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  printData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'confirm-print', 'refresh']);

// 响应式数据
const loading = ref(false);
const renderLoading = ref(false);
const printing = ref(false);
const downloading = ref(false);
const scale = ref(1);
const minScale = 0.5;
const maxScale = 2;

// 分页配置
const pageConfig = ref({
  maxHeight: 800,        // 页面最大高度 (mm转px，大约A4纸高度)
  labelSpacing: 10,      // 标签间距 (px)
  pageMargin: 40,        // 页面边距 (px)
  estimatedLabelHeight: 80 // 预估标签高度 (px)
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const previewAreaStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: 'center top',
  transition: 'transform 0.3s ease',
  padding: '20px'
}));

// 分页模板数据
const paginatedTemplates = computed(() => {
  if (!props.printData?.processedTemplates?.length) {
    return [];
  }

  const templates = props.printData.processedTemplates.map((template, index) => ({
    ...template,
    originalIndex: index
  }));

  // 根据标签高度进行分页
  const pages = [];
  let currentPage = [];
  let currentPageHeight = pageConfig.value.pageMargin * 2; // 初始页面边距

  templates.forEach((template, index) => {
    const estimatedHeight = pageConfig.value.estimatedLabelHeight + pageConfig.value.labelSpacing;

    // 检查是否需要换页
    if (currentPageHeight + estimatedHeight > pageConfig.value.maxHeight && currentPage.length > 0) {
      pages.push([...currentPage]);
      currentPage = [];
      currentPageHeight = pageConfig.value.pageMargin * 2;
    }

    currentPage.push(template);
    currentPageHeight += estimatedHeight;
  });

  // 添加最后一页
  if (currentPage.length > 0) {
    pages.push(currentPage);
  }

  return pages;
});

// 获取单个打印页面样式（带间距效果）
const getPrintPageStyle = (pageIndex) => ({
  background: 'white',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  padding: `${pageConfig.value.pageMargin}px`,
  margin: pageIndex === 0 ? '0 auto 30px auto' : '30px auto',
  maxWidth: '210mm', // A4纸宽度
  minHeight: `${pageConfig.value.maxHeight}px`,
  overflow: 'hidden',
  position: 'relative'
});

// 获取标签项样式
const getLabelItemStyle = (labelIndex) => ({
  marginBottom: labelIndex === 0 ? '0' : `${pageConfig.value.labelSpacing}px`,
  position: 'relative'
});

// 初始化hiprint
const initHiprint = async () => {
  try {
    // 使用工具模块初始化hiprint
    await initHiprintPlugin();
    console.log('hiprint初始化成功');
  } catch (error) {
    console.error('初始化hiprint失败:', error);
    throw error;
  }
};

// 渲染预览
const renderPreview = async () => {
  if (!props.printData?.processedTemplates?.length) {
    throw new Error('没有可预览的模板数据');
  }

  renderLoading.value = true;

  try {
    // 确保hiprint已初始化
    await initHiprint();

    // 循环渲染所有模板
    for (let i = 0; i < props.printData.processedTemplates.length; i++) {
      const templateData = props.printData.processedTemplates[i];

      if (!templateData?.panels?.[0]) {
        console.warn(`模板 ${i} 数据无效，跳过渲染`);
        continue;
      }

      // 使用工具模块创建hiprint模板实例
      const hiprintTemplate = createPrintTemplate({
        panels: templateData.panels
      }, {
        // 不需要设置settingContainer，因为这是预览模式
        dataMode: 1
      });

      // 获取HTML内容并渲染
      await nextTick();
      const container = document.getElementById(`hiprint-preview-container-${i}`);
      if (container && hiprintTemplate) {
        // 使用getHtml方法获取渲染后的HTML
        const htmlContent = hiprintTemplate.getHtml();
        if (htmlContent && htmlContent.length > 0) {
          // 清空容器并添加新内容
          container.innerHTML = '';
          if (typeof htmlContent === 'string') {
            container.innerHTML = htmlContent;
          } else if (htmlContent[0]) {
            // 如果是jQuery对象或DOM元素数组
            if (htmlContent[0].innerHTML) {
              container.innerHTML = htmlContent[0].innerHTML;
            } else {
              container.appendChild(htmlContent[0]);
            }
          }

          // 渲染完成后，动态调整页面高度配置
          await nextTick();
          adjustPageConfigBasedOnActualHeight(container, i);
        } else {
          throw new Error(`模板 ${i} 获取的HTML内容为空`);
        }
      } else {
        throw new Error(`模板 ${i} 预览容器不存在`);
      }
    }
  } catch (error) {
    console.error('渲染预览失败:', error);
    const errorMessage = handleHiprintError ? handleHiprintError(error) : error.message;

    // 在第一个容器显示错误信息
    const container = document.getElementById('hiprint-preview-container-0');
    if (container) {
      container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #999;">
          <p>预览渲染失败</p>
          <p style="font-size: 12px; margin-top: 8px;">${errorMessage}</p>
        </div>
      `;
    }
    throw error;
  } finally {
    renderLoading.value = false;
  }
};

// 根据实际渲染高度调整页面配置
const adjustPageConfigBasedOnActualHeight = (container, index) => {
  if (container && index === 0) { // 只在第一个标签渲染完成后调整
    const actualHeight = container.offsetHeight;
    if (actualHeight > 0 && actualHeight !== pageConfig.value.estimatedLabelHeight) {
      pageConfig.value.estimatedLabelHeight = Math.max(actualHeight, 60); // 最小高度60px
      console.log(`调整标签预估高度为: ${pageConfig.value.estimatedLabelHeight}px`);
    }
  }
};

const handleZoomIn = () => {
  if (scale.value < maxScale) {
    scale.value = Math.min(scale.value + 0.1, maxScale);
  }
};

const handleZoomOut = () => {
  if (scale.value > minScale) {
    scale.value = Math.max(scale.value - 0.1, minScale);
  }
};

const handleRefresh = async () => {
  loading.value = true;
  try {
    await renderPreview();
    emit('refresh');
    EleMessage.success('预览已刷新');
  } catch (error) {
    EleMessage.error('刷新预览失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

const handlePrint = async () => {
  if (!paginatedTemplates.value.length) {
    EleMessage.error('没有可打印的模板数据，请先刷新预览');
    return;
  }

  printing.value = true;
  try {
    // 检查 hiprint 客户端连接状态
    if (!window.hiwebSocket || !window.hiwebSocket.opened) {
      showClientNotConnectedError();
      return;
    }

    // 使用 hiprint 客户端打印
    await printWithHiprintClient();

    emit('confirm-print', {
      ...props.printData,
      action: 'print'
    });

    EleMessage.success(`打印任务已发送 (共${paginatedTemplates.value.length}页)`);
  } catch (error) {
    EleMessage.error('打印失败：' + error.message);
  } finally {
    printing.value = false;
  }
};

// 显示客户端未连接错误
const showClientNotConnectedError = () => {
  const host = window.hiwebSocket?.host || 'localhost:17521';
  EleMessage.error({
    title: '客户端未连接',
    message: `连接【${host}】失败！请确保已下载并运行打印服务！`,
    duration: 5000,
    showClose: true,
    dangerouslyUseHTMLString: true,
    message: `
      <div>
        连接【${host}】失败！<br/>
        请确保目标服务器已
        <a href="https://gitee.com/CcSimple/electron-hiprint/releases" target="_blank">下载</a>
        并
        <a href="hiprint://" target="_blank">运行</a>
        打印服务！
      </div>
    `
  });
};

// 使用 hiprint 客户端打印
const printWithHiprintClient = async () => {
  try {
    // 确保 hiprint 已初始化
    await initHiprint();

    // 准备打印数据 - 为每个模板创建独立的打印任务
    const printTasks = [];

    for (let pageIndex = 0; pageIndex < paginatedTemplates.value.length; pageIndex++) {
      const page = paginatedTemplates.value[pageIndex];

      for (let labelIndex = 0; labelIndex < page.length; labelIndex++) {
        const template = page[labelIndex];
        const originalIndex = template.originalIndex;

        // 获取对应的打印数据
        const printDataItem = props.printData?.items?.[originalIndex];
        if (printDataItem) {
          printTasks.push({
            templateData: {
              panels: template.panels
            },
            printData: printDataItem,
            title: `标签打印_${originalIndex + 1}`
          });
        }
      }
    }

    if (printTasks.length === 0) {
      throw new Error('没有找到可打印的数据');
    }

    // 获取打印机列表
    const hiprintInstance = getHiprintInstance();
    if (!hiprintInstance) {
      throw new Error('hiprint 实例未初始化');
    }

    // 创建临时模板进行打印
    const printerList = hiprintInstance.getPrinterList ? hiprintInstance.getPrinterList() : [];
    console.log('可用打印机列表:', printerList);

    // 批量打印所有任务
    if (printTasks.length === 1) {
      // 单个打印任务
      await printSingleTask(printTasks[0]);
    } else {
      // 多个打印任务 - 使用分批打印
      await printMultipleTasks(printTasks);
    }

  } catch (error) {
    console.error('hiprint 客户端打印失败:', error);
    throw error;
  }
};

// 打印单个任务
const printSingleTask = async (task) => {
  const hiprintTemplate = createPrintTemplate(task.templateData, {
    dataMode: 1
  });

  if (!hiprintTemplate || typeof hiprintTemplate.print2 !== 'function') {
    throw new Error('创建打印模板失败');
  }

  // 添加打印成功回调
  hiprintTemplate.on('printSuccess', function() {
    console.log('打印成功:', task.title);
    EleMessage.success(`${task.title} 打印成功`);
  });

  hiprintTemplate.on('printError', function(error) {
    console.error('打印失败:', error);
    EleMessage.error(`${task.title} 打印失败: ${error.message || error}`);
  });

  // 使用 print2 方法进行客户端打印
  hiprintTemplate.print2(task.printData, {
    printer: '', // 使用默认打印机
    title: task.title || '标签打印'
  });
};

// 打印多个任务
const printMultipleTasks = async (tasks) => {
  // 使用第一个任务的模板结构
  const firstTask = tasks[0];
  const hiprintTemplate = createPrintTemplate(firstTask.templateData, {
    dataMode: 1
  });

  if (!hiprintTemplate || typeof hiprintTemplate.print2 !== 'function') {
    throw new Error('创建打印模板失败');
  }

  // 添加批量打印回调
  hiprintTemplate.on('printSuccess', function() {
    console.log('批量打印成功');
    EleMessage.success(`批量打印成功 (共${tasks.length}个标签)`);
  });

  hiprintTemplate.on('printError', function(error) {
    console.error('批量打印失败:', error);
    EleMessage.error(`批量打印失败: ${error.message || error}`);
  });

  // 准备批量打印数据
  const batchPrintData = tasks.map(task => task.printData);

  // 使用分批打印功能
  hiprintTemplate.print2(batchPrintData, {
    printer: '', // 使用默认打印机
    title: `批量标签打印_${tasks.length}个`,
    printByFragments: true, // 启用分批打印
    generateHTMLInterval: 50, // 生成HTML间隔
    fragmentSize: 50000, // 分片大小
    sendInterval: 30 // 发送间隔
  });
};

const handleDownload = async () => {
  if (!paginatedTemplates.value.length) {
    EleMessage.error('没有可下载的模板数据，请先刷新预览');
    return;
  }

  downloading.value = true;
  try {
    // 使用 hiprint 导出 PDF
    await downloadWithHiprint();

    emit('confirm-print', {
      ...props.printData,
      action: 'download'
    });

    EleMessage.success('PDF导出成功');
  } catch (error) {
    EleMessage.error('下载失败：' + error.message);
  } finally {
    downloading.value = false;
  }
};

// 使用 hiprint 导出 PDF
const downloadWithHiprint = async () => {
  try {
    // 确保 hiprint 已初始化
    await initHiprint();

    // 如果只有一个模板，直接导出
    if (paginatedTemplates.value.length === 1 && paginatedTemplates.value[0].length === 1) {
      const template = paginatedTemplates.value[0][0];
      const printDataItem = props.printData?.items?.[template.originalIndex];

      if (printDataItem) {
        const hiprintTemplate = createPrintTemplate({
          panels: template.panels
        }, {
          dataMode: 1
        });

        if (hiprintTemplate && typeof hiprintTemplate.toPdf === 'function') {
          const fileName = `${props.printData?.templateName || '标签打印'}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.pdf`;
          await hiprintTemplate.toPdf(printDataItem, fileName, {
            isDownload: true
          });
          return;
        }
      }
    }

    // 多个模板的情况，通过后端API下载
    emit('confirm-print', {
      ...props.printData,
      action: 'download'
    });

  } catch (error) {
    console.error('hiprint PDF导出失败:', error);
    // 如果 hiprint 导出失败，回退到后端API
    emit('confirm-print', {
      ...props.printData,
      action: 'download'
    });
  }
};

// 监听visible变化，重置缩放比例并渲染预览
watch(visible, async (newVal) => {
  if (newVal) {
    scale.value = 1;
    // 延迟一点时间确保DOM渲染完成
    await nextTick();
    setTimeout(async () => {
      try {
        await renderPreview();
      } catch (error) {
        console.error('自动渲染预览失败:', error);
      }
    }, 100);
  }
});

// 暴露方法供外部调用
defineExpose({
  handleRefresh,
  handleZoomIn,
  handleZoomOut,
  getPrintPageStyle,
  getLabelItemStyle,
  paginatedTemplates
});
</script>

<style scoped>
.enhanced-preview-drawer {
  /* 抽屉样式 */
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 16px;
}

.drawer-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drawer-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.drawer-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.print-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.print-info-bar {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.info-details {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.preview-area {
  flex: 1;
  overflow: auto;
  background: #f0f2f5;
  border-radius: 8px;
  min-height: 400px;
}

.print-page {
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 打印纸阴影效果 */
.print-page::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 8px;
  right: 8px;
  height: 8px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), transparent);
  border-radius: 0 0 6px 6px;
  z-index: -1;
}

.print-page::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 16px;
  right: 16px;
  height: 8px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.05), transparent);
  border-radius: 0 0 4px 4px;
  z-index: -2;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.page-number {
  font-weight: 600;
  color: #409eff;
}

.page-info {
  color: #909399;
}

/* 标签容器样式 */
.labels-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.label-item {
  flex-shrink: 0;
  break-inside: avoid;
}

.hiprint-preview-container {
  min-height: 60px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 10px;
}

/* hiprint生成的内容样式 */
.hiprint-preview-container :deep(.hiprint-printPanel) {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  transform-origin: top center;
  max-width: 100%;
  height: auto;
}

.hiprint-preview-container :deep(.hiprint-printElement) {
  box-sizing: border-box;
}

.hiprint-preview-container :deep(.hiprint-printElement-text) {
  word-break: break-word;
  overflow-wrap: break-word;
}

.hiprint-preview-container :deep(.hiprint-printElement-qrcode) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.hiprint-preview-container :deep(.hiprint-printElement-qrcode img) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style>
